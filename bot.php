<?php

// Include configuration file
require_once 'config.php';
// Include texts file
require_once 'texts.php';
// Include notification system
require_once 'notify.php';

define('API_KEY', $API_KEY);

function bot($method, $datas = [])
{
    $url = "https://api.telegram.org/bot" . API_KEY . "/" . $method;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $datas);
    $res = curl_exec($ch);
    if (curl_error($ch)) {
        var_dump(curl_error($ch));
    } else {
        return json_decode($res);
    }
}

function sendmessage($chat_id, $text, $reply_markup = null)
{
    $data = [
        'chat_id' => $chat_id,
        'text' => $text,
        'parse_mode' => 'HTML'
    ];

    if ($reply_markup) {
        $data['reply_markup'] = $reply_markup;
    }

    bot('sendMessage', $data);
}

function checkUserMembership($user_id, $channels)
{
    foreach ($channels as $channel) {
        $result = bot('getChatMember', [
            'chat_id' => $channel,
            'user_id' => $user_id
        ]);

        if (!$result || !isset($result->result) ||
            in_array($result->result->status, ['left', 'kicked'])) {
            return false;
        }
    }
    return true;
}

function createJoinKeyboard($channels)
{
    $keyboard = [];

    // Create a single row with all channel buttons side by side
    $channel_buttons = [];
    $channel_names = ['کانال اول', 'کانال دوم'];

    foreach ($channels as $index => $channel) {
        $channel_name = str_replace('@', '', $channel);
        $button_text = isset($channel_names[$index]) ? $channel_names[$index] : "کانال " . ($index + 1);
        $channel_buttons[] = [
            'text' => "🔔 عضویت در $button_text",
            'url' => "https://t.me/$channel_name"
        ];
    }
    // Reverse the array to put "کانال اول" on the right
    $channel_buttons = array_reverse($channel_buttons);
    $keyboard[] = $channel_buttons;

    // Add check membership button in a separate row
    $keyboard[] = [
        [
            'text' => '🔄 بررسی عضویت',
            'callback_data' => 'check_membership'
        ]
    ];

    return json_encode(['inline_keyboard' => $keyboard]);
}

$update = json_decode(file_get_contents('php://input'));

// Handle inline queries for anonymous messaging
if (isset($update->inline_query)) {
    $inline_query = $update->inline_query;
    $query = $inline_query->query;
    $user_id = $inline_query->from->id;

    // Check if user is member of required channels
    if (!checkUserMembership($user_id, $required_channels)) {
        // Return empty results if not member
        bot('answerInlineQuery', [
            'inline_query_id' => $inline_query->id,
            'results' => json_encode([]),
            'cache_time' => 0,
            'is_personal' => true,
            'switch_pm_text' => 'ابتدا عضو شوید',
            'switch_pm_parameter' => 'join'
        ]);
        exit;
    }

    $results = [];

    if (empty($query)) {
        // Show help when query is empty
        $results[] = [
            'type' => 'article',
            'id' => 'help',
            'title' => '📚 راهنمای ارسال نجوا',
            'description' => 'برای ارسال پیام ناشناس: TEXT متن USERNAME یوزرنیم',
            'input_message_content' => [
                'message_text' => "📚 راهنمای ارسال پیام ناشناس\n\nبرای ارسال نجوا به شکل زیر عمل کنید:\n\n<code>@NajvaGram_Bot TEXT متن شما USERNAME @یوزرنیم_مقصد</code>\n\nمثال:\n<code>@NajvaGram_Bot TEXT سلام چطوری؟ USERNAME @john</code>",
                'parse_mode' => 'HTML'
            ]
        ];
    } else {
        // Parse the query: TEXT message USERNAME target_username
        if (preg_match('/^TEXT\s+(.+?)\s+USERNAME\s+@?(\w+)$/i', $query, $matches)) {
            $message_text = trim($matches[1]);
            $target_username = trim($matches[2]);

            if (!empty($message_text) && !empty($target_username)) {
                // Check if target user exists and can receive messages
                $target_user_id = getUserIdByUsername($target_username);

                if ($target_user_id && !canReceiveAnonymousMessages($target_user_id)) {
                    // User has privacy enabled
                    $results[] = [
                        'type' => 'article',
                        'id' => 'privacy_enabled',
                        'title' => '🔒 حریم خصوصی فعال',
                        'description' => "کاربر @$target_username حریم خصوصی فعال دارد",
                        'input_message_content' => [
                            'message_text' => "🔒 متأسفانه کاربر @$target_username حریم خصوصی فعال دارد و نمی‌تواند پیام ناشناس دریافت کند.\n\nلطفاً با کاربر مقصد تماس بگیرید تا حریم خصوصی خود را غیرفعال کند.",
                            'parse_mode' => 'HTML'
                        ]
                    ];
                } else {
                    // Create anonymous message
                    $anonymous_id = generateAnonymousId();

                    // Store the anonymous message
                    storeAnonymousMessage($anonymous_id, $user_id, $target_username, $message_text);

                    // Send notification to target user
                    notifyUserNewAnonymousMessage($target_username, $message_text);

                    $results[] = [
                        'type' => 'article',
                        'id' => 'send_' . $anonymous_id,
                        'title' => '💌 ارسال پیام ناشناس',
                        'description' => "به @$target_username: " . mb_substr($message_text, 0, 50) . (mb_strlen($message_text) > 50 ? '...' : ''),
                        'input_message_content' => [
                            'message_text' => "💌 پیام ناشناس با موفقیت ارسال شد!\n\n👤 مقصد: @$target_username\n📝 پیام: $message_text\n\n✅ پیام شما به صورت کاملاً ناشناس ارسال شده است.",
                            'parse_mode' => 'HTML'
                        ]
                    ];
                }
            }
        } else {
            // Show format help
            $results[] = [
                'type' => 'article',
                'id' => 'format_help',
                'title' => '❌ فرمت اشتباه',
                'description' => 'فرمت صحیح: TEXT متن USERNAME یوزرنیم',
                'input_message_content' => [
                    'message_text' => "❌ فرمت وارد شده اشتباه است!\n\nفرمت صحیح:\n<code>TEXT متن شما USERNAME @یوزرنیم_مقصد</code>\n\nمثال:\n<code>TEXT سلام چطوری؟ USERNAME @john</code>",
                    'parse_mode' => 'HTML'
                ]
            ];
        }
    }

    bot('answerInlineQuery', [
        'inline_query_id' => $inline_query->id,
        'results' => json_encode($results),
        'cache_time' => 0,
        'is_personal' => true
    ]);

    exit;
}

// Handle callback queries (button presses)
if (isset($update->callback_query)) {
    $callback_query = $update->callback_query;
    $user_id = $callback_query->from->id;
    $chat_id = $callback_query->message->chat->id;
    $data = $callback_query->data;

    if ($data == 'check_membership') {
        $join_keyboard = createJoinKeyboard($required_channels);

        // First, show checking message with buttons
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => "⌛️ در حال بررسی عضویت شما...",
            'reply_markup' => $join_keyboard
        ]);

        // Small delay to show the checking message
        sleep(1);

        if (checkUserMembership($user_id, $required_channels)) {
            $first_name = $callback_query->from->first_name ?? 'کاربر';
            $user_lang = BotTexts::getUserLanguage($user_id);

            $main_menu_keyboard = BotTexts::createMainMenuKeyboard($user_lang);
            $welcome_text = BotTexts::getWelcomeText($first_name, $user_lang);

            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => $welcome_text,
                'parse_mode' => 'HTML',
                'reply_markup' => $main_menu_keyboard
            ]);
        } else {
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => BotTexts::getNotMemberText(),
                'parse_mode' => 'HTML',
                'reply_markup' => $join_keyboard
            ]);
        }
    }

    // Handle language selection
    elseif ($data == 'language') {
        $user_lang = BotTexts::getUserLanguage($user_id);
        $language_keyboard = BotTexts::createLanguageKeyboard($user_lang);
        $language_text = BotTexts::getLanguageText($user_lang);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $language_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $language_keyboard
        ]);
    }

    // Handle language change to Persian
    elseif ($data == 'set_lang_fa') {
        BotTexts::setUserLanguage($user_id, 'fa');
        $confirmation_text = BotTexts::getLanguageChangedText('fa');

        bot('answerCallbackQuery', [
            'callback_query_id' => $callback_query->id,
            'text' => $confirmation_text,
            'show_alert' => true
        ]);

        // Show main menu with new language
        $first_name = $callback_query->from->first_name ?? 'کاربر';
        $main_menu_keyboard = BotTexts::createMainMenuKeyboard('fa');
        $welcome_text = BotTexts::getWelcomeText($first_name, 'fa');

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $welcome_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $main_menu_keyboard
        ]);
    }

    // Handle language change to English
    elseif ($data == 'set_lang_en') {
        BotTexts::setUserLanguage($user_id, 'en');
        $confirmation_text = BotTexts::getLanguageChangedText('en');

        bot('answerCallbackQuery', [
            'callback_query_id' => $callback_query->id,
            'text' => $confirmation_text,
            'show_alert' => true
        ]);

        // Show main menu with new language
        $first_name = $callback_query->from->first_name ?? 'User';
        $main_menu_keyboard = BotTexts::createMainMenuKeyboard('en');
        $welcome_text = BotTexts::getWelcomeText($first_name, 'en');

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $welcome_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $main_menu_keyboard
        ]);
    }

    // Handle anonymous section
    elseif ($data == 'najva_section') {
        $user_lang = BotTexts::getUserLanguage($user_id);

        // Check if user has username
        $user_data = getUserData($user_id);
        if (empty($user_data['username'])) {
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "❌ برای استفاده از بخش نجوا، ابتدا باید یوزرنیم تلگرام داشته باشید.\n\nلطفاً یوزرنیم خود را در تنظیمات تلگرام تنظیم کنید و مجدداً /start را ارسال کنید.",
                'parse_mode' => 'HTML',
                'reply_markup' => BotTexts::createBackToMenuKeyboard($user_lang)
            ]);
            return;
        }

        // Get anonymous messages for user
        $anonymous_messages = getAnonymousMessagesForUser($user_data['username']);
        $message_count = count($anonymous_messages);

        $najva_keyboard = BotTexts::createNajvaSectionKeyboard($user_lang);
        $najva_text = BotTexts::getNajvaSectionText($user_lang);

        if ($message_count > 0) {
            $najva_text .= "\n\n💌 شما $message_count پیام ناشناس دریافت کرده‌اید!";
        }

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $najva_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $najva_keyboard
        ]);
    }

    // Handle view anonymous messages
    elseif ($data == 'view_anonymous_messages') {
        $user_lang = BotTexts::getUserLanguage($user_id);
        $user_data = getUserData($user_id);

        if (empty($user_data['username'])) {
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "❌ برای مشاهده پیام‌های ناشناس، ابتدا باید یوزرنیم تلگرام داشته باشید.",
                'parse_mode' => 'HTML',
                'reply_markup' => BotTexts::createBackToMenuKeyboard($user_lang)
            ]);
            return;
        }

        $anonymous_messages = getAnonymousMessagesForUser($user_data['username']);

        if (empty($anonymous_messages)) {
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "📭 شما هیچ پیام ناشناسی دریافت نکرده‌اید.\n\nبرای دریافت پیام ناشناس، یوزرنیم خود (@{$user_data['username']}) را با دوستان خود به اشتراک بگذارید.",
                'parse_mode' => 'HTML',
                'reply_markup' => BotTexts::createNajvaSectionKeyboard($user_lang)
            ]);
            return;
        }

        // Show messages
        $messages_text = "💌 پیام‌های ناشناس شما:\n\n";
        $counter = 1;

        foreach ($anonymous_messages as $id => $message) {
            $date = date('Y/m/d H:i', $message['created_at']);
            $messages_text .= "📝 پیام $counter:\n";
            $messages_text .= "💬 متن: {$message['message_text']}\n";
            $messages_text .= "📅 تاریخ: $date\n";
            $messages_text .= "👁 وضعیت: " . ($message['viewed'] ? 'خوانده شده ✅' : 'خوانده نشده 🔴') . "\n\n";

            // Mark as viewed
            markAnonymousMessageViewed($id);

            $counter++;

            // Limit to 5 messages per page to avoid message length issues
            if ($counter > 5) {
                $messages_text .= "... و " . (count($anonymous_messages) - 5) . " پیام دیگر\n\n";
                break;
            }
        }

        $messages_text .= "⚠️ تمام پیام‌های نمایش داده شده به عنوان خوانده شده علامت‌گذاری شدند.";

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $messages_text,
            'parse_mode' => 'HTML',
            'reply_markup' => BotTexts::createNajvaSectionKeyboard($user_lang)
        ]);
    }

    // Handle najva help
    elseif ($data == 'najva_help') {
        $user_lang = BotTexts::getUserLanguage($user_id);
        $help_keyboard = BotTexts::createNajvaHelpKeyboard($user_lang);
        $help_text = BotTexts::getNajvaHelpText($user_lang);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $help_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $help_keyboard
        ]);
    }

    // Handle najva settings
    elseif ($data == 'najva_settings') {
        $user_lang = BotTexts::getUserLanguage($user_id);
        $settings_keyboard = BotTexts::createNajvaSettingsKeyboard($user_lang);
        $settings_text = BotTexts::getNajvaSettingsText($user_lang);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $settings_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $settings_keyboard
        ]);
    }

    // Handle privacy section
    elseif ($data == 'privacy') {
        $user_lang = BotTexts::getUserLanguage($user_id);
        $privacy_enabled = getUserPrivacy($user_id);
        $privacy_keyboard = BotTexts::createPrivacyKeyboard($user_lang, $privacy_enabled);
        $privacy_text = BotTexts::getPrivacyText($user_lang, $privacy_enabled);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $privacy_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $privacy_keyboard
        ]);
    }

    // Handle privacy toggle on
    elseif ($data == 'toggle_privacy_on') {
        setUserPrivacy($user_id, true);
        $user_lang = BotTexts::getUserLanguage($user_id);
        $confirmation_text = BotTexts::getPrivacyToggleText($user_lang, true);

        bot('answerCallbackQuery', [
            'callback_query_id' => $callback_query->id,
            'text' => $confirmation_text,
            'show_alert' => true
        ]);

        // Update the privacy page
        $privacy_keyboard = BotTexts::createPrivacyKeyboard($user_lang, true);
        $privacy_text = BotTexts::getPrivacyText($user_lang, true);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $privacy_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $privacy_keyboard
        ]);
    }

    // Handle privacy toggle off
    elseif ($data == 'toggle_privacy_off') {
        setUserPrivacy($user_id, false);
        $user_lang = BotTexts::getUserLanguage($user_id);
        $confirmation_text = BotTexts::getPrivacyToggleText($user_lang, false);

        bot('answerCallbackQuery', [
            'callback_query_id' => $callback_query->id,
            'text' => $confirmation_text,
            'show_alert' => true
        ]);

        // Update the privacy page
        $privacy_keyboard = BotTexts::createPrivacyKeyboard($user_lang, false);
        $privacy_text = BotTexts::getPrivacyText($user_lang, false);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $privacy_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $privacy_keyboard
        ]);
    }

    // Handle main menu help
    elseif ($data == 'help') {
        $user_lang = BotTexts::getUserLanguage($user_id);
        $help_keyboard = BotTexts::createBackToMenuKeyboard($user_lang);
        $help_text = BotTexts::getHelpText($user_lang);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $help_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $help_keyboard
        ]);
    }

    // Handle support
    elseif ($data == 'support') {
        $user_lang = BotTexts::getUserLanguage($user_id);
        $support_keyboard = BotTexts::createBackToMenuKeyboard($user_lang);
        $support_text = BotTexts::getSupportText($user_lang);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $support_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $support_keyboard
        ]);
    }

    // Handle back to menu
    elseif ($data == 'back_to_menu') {
        $first_name = $callback_query->from->first_name ?? 'کاربر';
        $user_lang = BotTexts::getUserLanguage($user_id);

        $main_menu_keyboard = BotTexts::createMainMenuKeyboard($user_lang);
        $welcome_text = BotTexts::getWelcomeText($first_name, $user_lang);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $welcome_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $main_menu_keyboard
        ]);
    }

    exit;
}

// Handle regular messages
if (isset($update->message)) {
    $message = $update->message;
    $chat_id = $message->chat->id;
    $user_id = $message->from->id;
    $text = $message->text;

    // Store user username if available
    if (isset($message->from->username)) {
        storeUserUsername($user_id, $message->from->username);
    }

    if ($text == "/start") {
        $first_name = $message->from->first_name ?? 'کاربر';

        if (checkUserMembership($user_id, $required_channels)) {
            $user_lang = BotTexts::getUserLanguage($user_id);

            $main_menu_keyboard = BotTexts::createMainMenuKeyboard($user_lang);
            $welcome_text = BotTexts::getWelcomeText($first_name, $user_lang);

            sendmessage($chat_id, $welcome_text, $main_menu_keyboard);

            // Check for anonymous messages
            if (isset($message->from->username)) {
                $anonymous_messages = getAnonymousMessagesForUser($message->from->username);
                if (!empty($anonymous_messages)) {
                    $count = count($anonymous_messages);
                    sendmessage($chat_id, "💌 شما $count پیام ناشناس دریافت کرده‌اید!\n\nبرای مشاهده پیام‌ها از دکمه «بخش نجوا» استفاده کنید.");
                }
            }
        } else {
            $join_keyboard = createJoinKeyboard($required_channels);
            sendmessage($chat_id,
                BotTexts::getJoinChannelsText($first_name),
                $join_keyboard
            );
        }
    }
}
?>