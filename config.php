<?php

// Bot Configuration
$API_KEY = '8101377108:AAEcJlRMUDQZvoJkruQruRRMHhaZLkEv1N4';

// JSON Database Configuration
$USER_DATA_FILE = 'user_data.json';
$USER_LANGUAGES_FILE = 'user_languages.json';
$USER_PRIVACY_FILE = 'user_privacy.json';
$ANONYMOUS_MESSAGES_FILE = 'anonymous_messages.json';

// Required channels for mandatory join
$required_channels = [
    '@speedxteam',
    '@speedx_bots'
];

/**
 * JSON Database Helper Functions
 */

// Read JSON file
function readJsonFile($filename) {
    if (file_exists($filename)) {
        $content = file_get_contents($filename);
        return json_decode($content, true) ?: [];
    }
    return [];
}

// Write JSON file
function writeJsonFile($filename, $data) {
    return file_put_contents($filename, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

// Get user privacy setting
function getUserPrivacy($user_id) {
    global $USER_PRIVACY_FILE;
    $privacy_data = readJsonFile($USER_PRIVACY_FILE);
    return $privacy_data[$user_id] ?? true; // Default: privacy enabled
}

// Set user privacy setting
function setUserPrivacy($user_id, $privacy_enabled) {
    global $USER_PRIVACY_FILE;
    $privacy_data = readJsonFile($USER_PRIVACY_FILE);
    $privacy_data[$user_id] = $privacy_enabled;
    return writeJsonFile($USER_PRIVACY_FILE, $privacy_data);
}

// Get user data
function getUserData($user_id) {
    global $USER_DATA_FILE;
    $user_data = readJsonFile($USER_DATA_FILE);
    return $user_data[$user_id] ?? [];
}

// Set user data
function setUserData($user_id, $data) {
    global $USER_DATA_FILE;
    $user_data = readJsonFile($USER_DATA_FILE);
    $user_data[$user_id] = $data;
    return writeJsonFile($USER_DATA_FILE, $user_data);
}

/**
 * Anonymous Message Functions
 */

// Generate unique anonymous message ID
function generateAnonymousId() {
    return uniqid('anon_', true);
}

// Store anonymous message
function storeAnonymousMessage($anonymous_id, $sender_id, $target_username, $message_text) {
    global $ANONYMOUS_MESSAGES_FILE;
    $messages = readJsonFile($ANONYMOUS_MESSAGES_FILE);

    $messages[$anonymous_id] = [
        'sender_id' => $sender_id,
        'target_username' => strtolower(str_replace('@', '', $target_username)),
        'message_text' => $message_text,
        'created_at' => time(),
        'viewed' => false
    ];

    return writeJsonFile($ANONYMOUS_MESSAGES_FILE, $messages);
}

// Get anonymous messages for a user
function getAnonymousMessagesForUser($username) {
    global $ANONYMOUS_MESSAGES_FILE;
    $messages = readJsonFile($ANONYMOUS_MESSAGES_FILE);
    $user_messages = [];

    $username = strtolower(str_replace('@', '', $username));

    foreach ($messages as $id => $message) {
        if ($message['target_username'] === $username) {
            $user_messages[$id] = $message;
        }
    }

    return $user_messages;
}

// Mark anonymous message as viewed
function markAnonymousMessageViewed($anonymous_id) {
    global $ANONYMOUS_MESSAGES_FILE;
    $messages = readJsonFile($ANONYMOUS_MESSAGES_FILE);

    if (isset($messages[$anonymous_id])) {
        $messages[$anonymous_id]['viewed'] = true;
        return writeJsonFile($ANONYMOUS_MESSAGES_FILE, $messages);
    }

    return false;
}

// Check if user can receive anonymous messages (privacy check)
function canReceiveAnonymousMessages($user_id) {
    return !getUserPrivacy($user_id); // If privacy is disabled, user can receive messages
}

// Get user ID by username (from stored user data)
function getUserIdByUsername($username) {
    global $USER_DATA_FILE;
    $user_data = readJsonFile($USER_DATA_FILE);

    $username = strtolower(str_replace('@', '', $username));

    foreach ($user_data as $user_id => $data) {
        if (isset($data['username']) && strtolower($data['username']) === $username) {
            return $user_id;
        }
    }

    return null;
}

// Store user username when they start the bot
function storeUserUsername($user_id, $username) {
    if (empty($username)) return;

    $user_data = getUserData($user_id);
    $user_data['username'] = strtolower(str_replace('@', '', $username));
    setUserData($user_id, $user_data);
}

?>